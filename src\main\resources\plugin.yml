name: OfflineItemDelivery
version: 1.0.0
main: com.example.offlineitemdelivery.OfflineItemDeliveryPlugin
api-version: 1.20
author: YourName
description: A plugin for delivering items to offline players with MySQL persistence

commands:
  giveoffline:
    description: Give an item to an offline player
    usage: /giveoffline <player> <item> [amount] [data]
    permission: offlineitemdelivery.give
    aliases: [giveoff, offlinegive]
  
  offlinequeue:
    description: Manage the offline item delivery queue
    usage: /offlinequeue <list|clear|remove> [player]
    permission: offlineitemdelivery.admin
    aliases: [oqueue, itemqueue]

permissions:
  offlineitemdelivery.give:
    description: Allows giving items to offline players
    default: op
  
  offlineitemdelivery.admin:
    description: Allows managing the offline item queue
    default: op
  
  offlineitemdelivery.*:
    description: Gives access to all OfflineItemDelivery permissions
    default: op
    children:
      offlineitemdelivery.give: true
      offlineitemdelivery.admin: true
