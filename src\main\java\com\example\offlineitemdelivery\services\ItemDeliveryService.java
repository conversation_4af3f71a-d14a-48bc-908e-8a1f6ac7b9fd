package com.example.offlineitemdelivery.services;

import com.example.offlineitemdelivery.OfflineItemDeliveryPlugin;
import com.example.offlineitemdelivery.database.DatabaseManager;
import com.example.offlineitemdelivery.database.PendingItemDAO;
import com.example.offlineitemdelivery.models.PendingItem;
import com.example.offlineitemdelivery.utils.ValidationUtils;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import java.util.logging.Level;

public class ItemDeliveryService {
    
    private final OfflineItemDeliveryPlugin plugin;
    private final PendingItemDAO pendingItemDAO;
    
    public ItemDeliveryService(OfflineItemDeliveryPlugin plugin, DatabaseManager databaseManager) {
        this.plugin = plugin;
        this.pendingItemDAO = new PendingItemDAO(databaseManager);
    }
    
    /**
     * Adds an item to the delivery queue for an offline player
     */
    public boolean queueItemForPlayer(UUID playerUuid, String playerName, ItemStack itemStack, int amount) {
        return queueItemForPlayer(playerUuid, playerName, itemStack, amount, null);
    }

    /**
     * Adds an item to the delivery queue for an offline player with a specific target server
     */
    public boolean queueItemForPlayer(UUID playerUuid, String playerName, ItemStack itemStack, int amount, String targetServer) {
        // Validate input parameters
        if (playerUuid == null) {
            plugin.getLogger().warning("Cannot queue item: playerUuid is null");
            return false;
        }

        if (!ValidationUtils.isValidPlayerName(playerName)) {
            plugin.getLogger().warning("Cannot queue item: invalid player name '" + playerName + "'");
            return false;
        }

        if (!ValidationUtils.isValidItemStack(itemStack)) {
            plugin.getLogger().warning("Cannot queue item: invalid ItemStack for player " + playerName);
            return false;
        }

        if (!ValidationUtils.isValidAmount(amount)) {
            plugin.getLogger().warning("Cannot queue item: invalid amount " + amount + " for player " + playerName);
            return false;
        }

        try {
            // Check if player has too many pending items
            int currentPendingCount = pendingItemDAO.countPendingItems(playerUuid);
            int maxPendingItems = plugin.getConfig().getInt("settings.max-pending-items", 50);

            if (currentPendingCount >= maxPendingItems) {
                plugin.getLogger().warning("Player " + playerName + " has reached the maximum pending items limit (" + maxPendingItems + ")");
                return false;
            }

            // Sanitize inputs
            String sanitizedPlayerName = ValidationUtils.sanitizePlayerName(playerName);
            String sanitizedServerId = ValidationUtils.sanitizeServerId(
                targetServer != null ? targetServer : plugin.getServerId());

            // Create pending item
            PendingItem pendingItem = new PendingItem(playerUuid, sanitizedPlayerName, sanitizedServerId, itemStack, amount);

            // Add to database
            boolean success = pendingItemDAO.addPendingItem(pendingItem);

            if (success && plugin.getConfig().getBoolean("logging.log-deliveries", true)) {
                String serverInfo = targetServer != null ? " (target server: " + targetServer + ")" : "";
                plugin.getLogger().info("Queued item for player " + sanitizedPlayerName + ": " +
                    amount + "x " + itemStack.getType().name() + serverInfo);
            }

            return success;

        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to queue item for player " + playerName, e);
            return false;
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Unexpected error while queuing item for player " + playerName, e);
            return false;
        }
    }

    /**
     * Notifies a player about pending items without delivering them
     */
    public void notifyPendingItems(Player player) {
        try {
            // Get pending items count for this player (all servers)
            int pendingCount = pendingItemDAO.countPendingItems(player.getUniqueId());

            if (pendingCount == 0) {
                return;
            }

            // Notify player about pending items
            if (plugin.getConfig().getBoolean("settings.notify-on-join", true)) {
                String message = plugin.getConfig().getString("settings.pending-items-message",
                    "&eYou have &b{count}&e pending item(s) waiting for delivery!");
                message = message.replace("{count}", String.valueOf(pendingCount));
                plugin.sendMessage(player, message);

                // Add instruction to use the claim command
                String instructionMessage = plugin.getConfig().getString("settings.claim-instruction-message",
                    "&7Use &f/claimitems&7 to claim your items or &f/claimitems list&7 to see them.");
                plugin.sendMessage(player, instructionMessage);
            }

        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to check pending items for " + player.getName(), e);
        }
    }

    /**
     * Attempts to deliver all pending items to a player when they use the claim command
     */
    public void deliverPendingItems(Player player) {
        try {
            // Get pending items for this server only (to prevent duplicate deliveries)
            List<PendingItem> pendingItems = pendingItemDAO.getPendingItemsByServer(
                player.getUniqueId(), plugin.getServerId());
            
            if (pendingItems.isEmpty()) {
                return;
            }
            
            // Notify player about pending items
            if (plugin.getConfig().getBoolean("settings.notify-on-join", true)) {
                String message = plugin.getConfig().getString("settings.pending-items-message",
                    "&eYou have &b{count}&e pending item(s) waiting for delivery!");
                message = message.replace("{count}", String.valueOf(pendingItems.size()));
                plugin.sendMessage(player, message);
            }
            
            // Attempt to deliver each item
            for (PendingItem pendingItem : pendingItems) {
                if (deliverSingleItem(player, pendingItem)) {
                    // Mark as delivered in database
                    pendingItemDAO.markAsDelivered(pendingItem.getId());
                    
                    if (plugin.getConfig().getBoolean("logging.log-deliveries", true)) {
                        plugin.getLogger().info("Delivered item to " + player.getName() + ": " + 
                            pendingItem.getAmount() + "x " + pendingItem.getItemStack().getType().name());
                    }
                } else {
                    // Inventory full - notify player and stop trying
                    String message = plugin.getConfig().getString("settings.inventory-full-message",
                        "&cYour inventory is full! You have pending items waiting. Make some space and rejoin to receive them.");
                    plugin.sendMessage(player, message);

                    if (plugin.getConfig().getBoolean("logging.log-failures", true)) {
                        plugin.getLogger().info("Failed to deliver item to " + player.getName() +
                            " (inventory full): " + pendingItem.getAmount() + "x " +
                            pendingItem.getItemStack().getType().name());
                    }
                    break;
                }
            }
            
        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to deliver pending items to " + player.getName(), e);
        }
    }
    
    /**
     * Attempts to deliver a single item to a player
     */
    private boolean deliverSingleItem(Player player, PendingItem pendingItem) {
        ItemStack itemToGive = pendingItem.getItemStack().clone();
        itemToGive.setAmount(pendingItem.getAmount());
        
        // Check if player has space in inventory
        if (!hasInventorySpace(player, itemToGive)) {
            return false;
        }
        
        // Give the item
        HashMap<Integer, ItemStack> leftover = player.getInventory().addItem(itemToGive);
        
        if (leftover.isEmpty()) {
            // Successfully delivered
            String message = plugin.getConfig().getString("settings.delivery-success-message",
                "&aYou have received &e{amount}x {item}&a!");
            message = message
                .replace("{amount}", String.valueOf(pendingItem.getAmount()))
                .replace("{item}", getItemDisplayName(pendingItem.getItemStack()));
            plugin.sendMessage(player, message);
            return true;
        } else {
            // This shouldn't happen if we checked space correctly, but handle it anyway
            return false;
        }
    }
    
    /**
     * Checks if player has enough inventory space for an item
     */
    private boolean hasInventorySpace(Player player, ItemStack item) {
        // Create a copy of the player's inventory to test
        ItemStack[] contents = player.getInventory().getStorageContents().clone();
        
        int remainingAmount = item.getAmount();
        int maxStackSize = item.getMaxStackSize();
        
        // Check existing stacks of the same item
        for (ItemStack stack : contents) {
            if (stack != null && stack.isSimilar(item)) {
                int spaceInStack = maxStackSize - stack.getAmount();
                if (spaceInStack > 0) {
                    remainingAmount -= spaceInStack;
                    if (remainingAmount <= 0) {
                        return true;
                    }
                }
            }
        }
        
        // Check empty slots
        for (ItemStack stack : contents) {
            if (stack == null) {
                remainingAmount -= maxStackSize;
                if (remainingAmount <= 0) {
                    return true;
                }
            }
        }
        
        return remainingAmount <= 0;
    }
    
    /**
     * Gets a display name for an item
     */
    private String getItemDisplayName(ItemStack item) {
        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            return item.getItemMeta().getDisplayName();
        }
        return item.getType().name().toLowerCase().replace('_', ' ');
    }
    
    /**
     * Gets pending items for a player
     */
    public List<PendingItem> getPendingItems(UUID playerUuid) throws SQLException {
        return pendingItemDAO.getPendingItems(playerUuid);
    }

    /**
     * Gets pending items for a player on a specific server
     */
    public List<PendingItem> getPendingItemsByServer(UUID playerUuid, String serverId) throws SQLException {
        return pendingItemDAO.getPendingItemsByServer(playerUuid, serverId);
    }
    
    /**
     * Gets count of pending items for a player
     */
    public int getPendingItemCount(UUID playerUuid) throws SQLException {
        return pendingItemDAO.countPendingItems(playerUuid);
    }
    
    /**
     * Clears all pending items for a player
     */
    public boolean clearPendingItems(UUID playerUuid) throws SQLException {
        return pendingItemDAO.clearPendingItems(playerUuid);
    }
    
    /**
     * Removes a specific pending item
     */
    public boolean removePendingItem(int itemId) throws SQLException {
        return pendingItemDAO.deleteItem(itemId);
    }
}
