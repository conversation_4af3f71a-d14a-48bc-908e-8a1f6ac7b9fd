package com.example.offlineitemdelivery.commands;

import com.example.offlineitemdelivery.OfflineItemDeliveryPlugin;
import com.example.offlineitemdelivery.models.PendingItem;
import com.example.offlineitemdelivery.services.ItemDeliveryService;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.sql.SQLException;
import java.util.List;

public class ClaimItemsCommand implements CommandExecutor {
    
    private final OfflineItemDeliveryPlugin plugin;
    private final ItemDeliveryService itemDeliveryService;
    
    public ClaimItemsCommand(OfflineItemDeliveryPlugin plugin, ItemDeliveryService itemDeliveryService) {
        this.plugin = plugin;
        this.itemDeliveryService = itemDeliveryService;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // Only players can claim items
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can use this command.");
            return true;
        }
        
        Player player = (Player) sender;
        
        // Check permission
        if (!player.hasPermission("offlineitemdelivery.claim")) {
            player.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            return true;
        }
        
        // Handle different subcommands
        if (args.length > 0) {
            String subCommand = args[0].toLowerCase();
            
            switch (subCommand) {
                case "list":
                case "check":
                    return handleListCommand(player);
                case "all":
                case "force":
                    return handleClaimCommand(player, true);
                default:
                    return handleClaimCommand(player, false);
            }
        } else {
            // Default behavior - attempt to claim items
            return handleClaimCommand(player, false);
        }
    }
    
    private boolean handleListCommand(Player player) {
        try {
            List<PendingItem> pendingItems = itemDeliveryService.getPendingItemsByServer(
                player.getUniqueId(), plugin.getServerId());
            
            if (pendingItems.isEmpty()) {
                String message = plugin.getConfig().getString("settings.no-items-message",
                    "&eYou have no pending items to claim!");
                player.sendMessage(ChatColor.translateAlternateColorCodes('&', message));
                return true;
            }
            
            player.sendMessage(ChatColor.GREEN + "=== Your Pending Items ===");
            
            int count = 0;
            for (PendingItem item : pendingItems) {
                count++;
                String itemName = getItemDisplayName(item);
                String createdAt = item.getCreatedAt() != null ?
                    item.getCreatedAt().toString().substring(0, 19) : "Unknown";

                // Show server restriction info
                String serverInfo = "";
                String currentServerId = plugin.getServerId();
                if (!item.getServerId().equals(currentServerId)) {
                    serverInfo = ChatColor.RED + " [Server: " + item.getServerId() + "]";
                } else {
                    serverInfo = ChatColor.GREEN + " [This Server]";
                }

                player.sendMessage(ChatColor.YELLOW + "" + count + ". " + ChatColor.WHITE +
                    item.getAmount() + "x " + itemName + serverInfo + ChatColor.GRAY + " (Created: " + createdAt + ")");
            }
            
            player.sendMessage(ChatColor.GREEN + "Total: " + pendingItems.size() + " pending items");
            player.sendMessage("");
            player.sendMessage(ChatColor.GRAY + "Legend: " + ChatColor.GREEN + "[This Server]" +
                ChatColor.GRAY + " = Can claim here, " + ChatColor.RED + "[Server: name]" +
                ChatColor.GRAY + " = Must join that server");
            player.sendMessage(ChatColor.AQUA + "Use " + ChatColor.WHITE + "/claimitems" +
                ChatColor.AQUA + " to attempt to claim items available on this server.");
            
        } catch (SQLException e) {
            player.sendMessage(ChatColor.RED + "An error occurred while checking your pending items.");
            plugin.getLogger().severe("Database error in claim list command for " + player.getName() + ": " + e.getMessage());
        }
        
        return true;
    }
    
    private boolean handleClaimCommand(Player player, boolean force) {
        try {
            // Get pending items count first
            int pendingCount = itemDeliveryService.getPendingItemCount(player.getUniqueId());
            
            if (pendingCount == 0) {
                String message = plugin.getConfig().getString("settings.no-items-message",
                    "&eYou have no pending items to claim!");
                player.sendMessage(ChatColor.translateAlternateColorCodes('&', message));
                return true;
            }
            
            // Show what they're about to claim
            player.sendMessage(ChatColor.YELLOW + "Attempting to claim " + pendingCount + " pending item(s)...");
            
            // Get current inventory space info
            int emptySlots = getEmptyInventorySlots(player);
            int minEmptySlots = plugin.getConfig().getInt("settings.min-empty-slots-warning", 5);

            if (!force && emptySlots < minEmptySlots) {
                player.sendMessage(ChatColor.YELLOW + "Warning: You only have " + emptySlots +
                    " empty inventory slots. Some items might not be delivered.");
                player.sendMessage(ChatColor.AQUA + "Use " + ChatColor.WHITE + "/claimitems all" +
                    ChatColor.AQUA + " to attempt claiming anyway, or make more space first.");
                player.sendMessage(ChatColor.GRAY + "Use " + ChatColor.WHITE + "/claimitems list" +
                    ChatColor.GRAY + " to see what items are waiting.");
                return true;
            }
            
            // Attempt to deliver items
            itemDeliveryService.deliverPendingItems(player);
            
            // Check how many items are still pending after delivery attempt
            int remainingCount = itemDeliveryService.getPendingItemCount(player.getUniqueId());
            int deliveredCount = pendingCount - remainingCount;
            
            if (deliveredCount > 0) {
                String message = plugin.getConfig().getString("settings.claim-success-message",
                    "&aSuccessfully claimed &e{count}&a item(s)!");
                message = message.replace("{count}", String.valueOf(deliveredCount));
                player.sendMessage(ChatColor.translateAlternateColorCodes('&', message));
            }
            
            if (remainingCount > 0) {
                // Check current inventory space to provide specific guidance
                int currentEmptySlots = getEmptyInventorySlots(player);

                if (currentEmptySlots == 0) {
                    // Inventory is completely full
                    String message = plugin.getConfig().getString("settings.inventory-full-remaining-message",
                        "&eYou still have &b{count}&e pending item(s) that couldn't be delivered because your inventory is full.");
                    message = message.replace("{count}", String.valueOf(remainingCount));
                    player.sendMessage(ChatColor.translateAlternateColorCodes('&', message));

                    player.sendMessage(ChatColor.AQUA + "Make some space and use " + ChatColor.WHITE +
                        "/claimitems" + ChatColor.AQUA + " again to claim the remaining items.");
                } else {
                    // Inventory has space, so items might be for a different server
                    String message = plugin.getConfig().getString("settings.server-restricted-message",
                        "&eYou still have &b{count}&e pending item(s) that couldn't be delivered on this server.");
                    message = message.replace("{count}", String.valueOf(remainingCount));
                    player.sendMessage(ChatColor.translateAlternateColorCodes('&', message));

                    String helpMessage = plugin.getConfig().getString("settings.server-restricted-help",
                        "&bThese items may be restricted to a different server. Try joining other servers in the network to claim them.");
                    player.sendMessage(ChatColor.translateAlternateColorCodes('&', helpMessage));

                    player.sendMessage(ChatColor.GRAY + "Use " + ChatColor.WHITE + "/claimitems list" +
                        ChatColor.GRAY + " to see which server each item is for.");
                }
            }
            
        } catch (SQLException e) {
            player.sendMessage(ChatColor.RED + "An error occurred while claiming your items.");
            plugin.getLogger().severe("Database error in claim command for " + player.getName() + ": " + e.getMessage());
        }
        
        return true;
    }
    
    private int getEmptyInventorySlots(Player player) {
        int emptySlots = 0;
        for (int i = 0; i < 36; i++) { // Main inventory slots (0-35)
            if (player.getInventory().getItem(i) == null) {
                emptySlots++;
            }
        }
        return emptySlots;
    }
    
    private String getItemDisplayName(PendingItem item) {
        if (item == null || item.getItemStack() == null) {
            return "Unknown Item";
        }
        
        try {
            if (item.getItemStack().hasItemMeta() && item.getItemStack().getItemMeta().hasDisplayName()) {
                return item.getItemStack().getItemMeta().getDisplayName();
            }
            return item.getItemStack().getType().name().toLowerCase().replace('_', ' ');
        } catch (Exception e) {
            return "Corrupted Item";
        }
    }
}
