package com.example.offlineitemdelivery.commands;

import com.example.offlineitemdelivery.OfflineItemDeliveryPlugin;
import com.example.offlineitemdelivery.models.PendingItem;
import com.example.offlineitemdelivery.services.ItemDeliveryService;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.sql.SQLException;
import java.util.List;

public class ClaimItemsCommand implements CommandExecutor {
    
    private final OfflineItemDeliveryPlugin plugin;
    private final ItemDeliveryService itemDeliveryService;
    
    public ClaimItemsCommand(OfflineItemDeliveryPlugin plugin, ItemDeliveryService itemDeliveryService) {
        this.plugin = plugin;
        this.itemDeliveryService = itemDeliveryService;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // Only players can claim items
        if (!(sender instanceof Player)) {
            plugin.sendMessage(sender, "&cOnly players can use this command.");
            return true;
        }

        Player player = (Player) sender;

        // Check permission
        if (!player.hasPermission("offlineitemdelivery.claim")) {
            plugin.sendMessage(player, "&cYou don't have permission to use this command.");
            return true;
        }
        
        // Handle different subcommands
        if (args.length > 0) {
            String subCommand = args[0].toLowerCase();
            
            switch (subCommand) {
                case "list":
                case "check":
                    return handleListCommand(player);
                case "all":
                case "force":
                    return handleClaimCommand(player, true);
                default:
                    return handleClaimCommand(player, false);
            }
        } else {
            // Default behavior - attempt to claim items
            return handleClaimCommand(player, false);
        }
    }
    
    private boolean handleListCommand(Player player) {
        try {
            // Get ALL pending items, not just for current server
            List<PendingItem> pendingItems = itemDeliveryService.getPendingItems(player.getUniqueId());
            
            if (pendingItems.isEmpty()) {
                String message = plugin.getConfig().getString("settings.no-items-message",
                    "&eYou have no items to claim!");
                plugin.sendMessage(player, message);
                return true;
            }
            
            plugin.sendMessage(player, "&a=== Your Unclaimed Items ===");
            
            int count = 0;
            for (PendingItem item : pendingItems) {
                count++;
                String itemName = getItemDisplayName(item);
                String createdAt = item.getCreatedAt() != null ?
                    item.getCreatedAt().toString().substring(0, 19) : "Unknown";

                // Show server restriction info
                String serverInfo = "";
                String currentServerId = plugin.getServerId();
                if (!item.getServerId().equals(currentServerId)) {
                    serverInfo = ChatColor.RED + " [" + item.getServerId() + "]";
                } else {
                    serverInfo = ChatColor.GREEN + " [This Server]";
                }

                plugin.sendMessage(player, "&f" + item.getAmount() + "x " + itemName + serverInfo);
            }
            
            plugin.sendMessage(player, "&aTotal: " + pendingItems.size() + " pending items");
            player.sendMessage("");
            plugin.sendMessage(player, "&bUse &f/claimitems&b to redeem items available on this server.");
            
        } catch (SQLException e) {
            plugin.sendMessage(player, "&cAn error occurred while checking your pending items.");
            plugin.getLogger().severe("Database error in claim list command for " + player.getName() + ": " + e.getMessage());
        }
        
        return true;
    }
    
    private boolean handleClaimCommand(Player player, boolean force) {
        try {
            // Get pending items count first
            int pendingCount = itemDeliveryService.getPendingItemCount(player.getUniqueId());
            
            if (pendingCount == 0) {
                String message = plugin.getConfig().getString("settings.no-items-message",
                    "&eYou have no pending items to claim!");
                plugin.sendMessage(player, message);
                return true;
            }

            // Show what they're about to claim
            plugin.sendMessage(player, "&eAttempting to claim " + pendingCount + " pending item(s)...");
            
            // Get current inventory space info
            int emptySlots = getEmptyInventorySlots(player);
            int minEmptySlots = plugin.getConfig().getInt("settings.min-empty-slots-warning", 5);

            if (!force && emptySlots < minEmptySlots) {
                plugin.sendMessage(player, "&eWarning: You only have " + emptySlots +
                    " empty inventory slots. Some items might not be delivered.");
                plugin.sendMessage(player, "&7Use &f/claimitems list&7 to see what items are claimable.");
                return true;
            }
            
            // Attempt to deliver items
            itemDeliveryService.deliverPendingItems(player);
            
            // Check how many items are still pending after delivery attempt
            int remainingCount = itemDeliveryService.getPendingItemCount(player.getUniqueId());
            int deliveredCount = pendingCount - remainingCount;
            
            if (deliveredCount > 0) {
                String message = plugin.getConfig().getString("settings.claim-success-message",
                    "&aSuccessfully claimed &e{count}&a item(s)!");
                message = message.replace("{count}", String.valueOf(deliveredCount));
                plugin.sendMessage(player, message);
            }
            
            if (remainingCount > 0) {
                // Check current inventory space to provide specific guidance
                int currentEmptySlots = getEmptyInventorySlots(player);

                if (currentEmptySlots == 0) {
                    // Inventory is completely full
                    String message = plugin.getConfig().getString("settings.inventory-full-remaining-message",
                        "&eYou still have &b{count}&e item(s) that couldn't be delivered because your inventory is full.");
                    message = message.replace("{count}", String.valueOf(remainingCount));
                    plugin.sendMessage(player, message);    

                    plugin.sendMessage(player, "&bMake some space and use &f/claimitems&b again to claim the remaining items.");
                } else {
                    // Inventory has space, so items might be for a different server
                    String message = plugin.getConfig().getString("settings.server-restricted-message",
                        "&eYou still have &b{count}&e item(s) that couldn't be delivered on this server.");
                    message = message.replace("{count}", String.valueOf(remainingCount));
                    plugin.sendMessage(player, message);

                    String helpMessage = plugin.getConfig().getString("settings.server-restricted-help",
                        "&bTry joining a different server to claim them.");
                    plugin.sendMessage(player, helpMessage);

                    plugin.sendMessage(player, "&7Use &f/claimitems list&7 to see which server each item is for.");
                }
            }
            
        } catch (SQLException e) {
            plugin.sendMessage(player, "&cAn error occurred while claiming your items.");
            plugin.getLogger().severe("Database error in claim command for " + player.getName() + ": " + e.getMessage());
        }
        
        return true;
    }
    
    private int getEmptyInventorySlots(Player player) {
        int emptySlots = 0;
        for (int i = 0; i < 36; i++) { // Main inventory slots (0-35)
            if (player.getInventory().getItem(i) == null) {
                emptySlots++;
            }
        }
        return emptySlots;
    }
    
    private String getItemDisplayName(PendingItem item) {
        if (item == null || item.getItemStack() == null) {
            return "Unknown Item";
        }
        
        try {
            if (item.getItemStack().hasItemMeta() && item.getItemStack().getItemMeta().hasDisplayName()) {
                return item.getItemStack().getItemMeta().getDisplayName();
            }
            return item.getItemStack().getType().name().toLowerCase().replace('_', ' ');
        } catch (Exception e) {
            return "Corrupted Item";
        }
    }
}
