package com.example.offlineitemdelivery.commands;

import com.example.offlineitemdelivery.OfflineItemDeliveryPlugin;
import com.example.offlineitemdelivery.services.ItemDeliveryService;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

public class GiveOfflineCommand implements CommandExecutor, TabCompleter {
    
    private final OfflineItemDeliveryPlugin plugin;
    private final ItemDeliveryService itemDeliveryService;
    
    public GiveOfflineCommand(OfflineItemDeliveryPlugin plugin, ItemDeliveryService itemDeliveryService) {
        this.plugin = plugin;
        this.itemDeliveryService = itemDeliveryService;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("offlineitemdelivery.give")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            return true;
        }
        
        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /" + label + " <player> <item> [amount] [data]");
            sender.sendMessage(ChatColor.GRAY + "Examples:");
            sender.sendMessage(ChatColor.GRAY + "  /" + label + " Steve diamond_sword 1");
            sender.sendMessage(ChatColor.GRAY + "  /" + label + " Alex apple 64");
            sender.sendMessage(ChatColor.GRAY + "  /" + label + " Bob hand (gives item in your hand)");
            return true;
        }
        
        String targetPlayerName = args[0];
        String itemName = args[1];
        int amount = 1;
        
        // Parse amount if provided
        if (args.length >= 3) {
            try {
                amount = Integer.parseInt(args[2]);
                if (amount <= 0) {
                    sender.sendMessage(ChatColor.RED + "Amount must be greater than 0.");
                    return true;
                }
                if (amount > 2304) { // 36 stacks of 64
                    sender.sendMessage(ChatColor.RED + "Amount is too large. Maximum is 2304.");
                    return true;
                }
            } catch (NumberFormatException e) {
                sender.sendMessage(ChatColor.RED + "Invalid amount: " + args[2]);
                return true;
            }
        }
        
        // Get target player
        OfflinePlayer targetPlayer = Bukkit.getOfflinePlayer(targetPlayerName);
        if (targetPlayer == null || (!targetPlayer.hasPlayedBefore() && !targetPlayer.isOnline())) {
            sender.sendMessage(ChatColor.RED + "Player '" + targetPlayerName + "' has never played on this server.");
            return true;
        }
        
        // Get item to give
        ItemStack itemToGive;
        if (itemName.equalsIgnoreCase("hand")) {
            if (!(sender instanceof Player)) {
                sender.sendMessage(ChatColor.RED + "Only players can use 'hand' as an item.");
                return true;
            }
            
            Player senderPlayer = (Player) sender;
            ItemStack handItem = senderPlayer.getInventory().getItemInMainHand();
            
            if (handItem == null || handItem.getType() == Material.AIR) {
                sender.sendMessage(ChatColor.RED + "You must be holding an item to use 'hand'.");
                return true;
            }
            
            itemToGive = handItem.clone();
        } else {
            // Parse material
            Material material;
            try {
                material = Material.valueOf(itemName.toUpperCase());
            } catch (IllegalArgumentException e) {
                sender.sendMessage(ChatColor.RED + "Invalid item: " + itemName);
                return true;
            }
            
            itemToGive = new ItemStack(material, 1);
        }
        
        // Check if player is online
        if (targetPlayer.isOnline()) {
            Player onlinePlayer = targetPlayer.getPlayer();
            
            // Try to give directly
            ItemStack giveItem = itemToGive.clone();
            giveItem.setAmount(amount);
            
            if (hasInventorySpace(onlinePlayer, giveItem)) {
                onlinePlayer.getInventory().addItem(giveItem);
                sender.sendMessage(ChatColor.GREEN + "Successfully gave " + amount + "x " + 
                    getItemDisplayName(itemToGive) + " to " + targetPlayer.getName() + " (online).");
                onlinePlayer.sendMessage(ChatColor.GREEN + "You received " + amount + "x " + 
                    getItemDisplayName(itemToGive) + " from " + sender.getName() + "!");
                return true;
            } else {
                // Queue it even though they're online (inventory full)
                sender.sendMessage(ChatColor.YELLOW + targetPlayer.getName() + " is online but their inventory is full. Item queued for delivery.");
            }
        }
        
        // Queue the item for offline delivery
        UUID targetUuid = targetPlayer.getUniqueId();
        boolean success = itemDeliveryService.queueItemForPlayer(targetUuid, targetPlayer.getName(), itemToGive, amount);
        
        if (success) {
            sender.sendMessage(ChatColor.GREEN + "Successfully queued " + amount + "x " + 
                getItemDisplayName(itemToGive) + " for " + targetPlayer.getName() + ".");
            sender.sendMessage(ChatColor.GRAY + "The item will be delivered when they next join the server.");
        } else {
            sender.sendMessage(ChatColor.RED + "Failed to queue item. The player may have reached their pending item limit.");
        }
        
        return true;
    }
    
    private boolean hasInventorySpace(Player player, ItemStack item) {
        ItemStack[] contents = player.getInventory().getStorageContents().clone();
        int remainingAmount = item.getAmount();
        int maxStackSize = item.getMaxStackSize();
        
        // Check existing stacks
        for (ItemStack stack : contents) {
            if (stack != null && stack.isSimilar(item)) {
                int spaceInStack = maxStackSize - stack.getAmount();
                if (spaceInStack > 0) {
                    remainingAmount -= spaceInStack;
                    if (remainingAmount <= 0) return true;
                }
            }
        }
        
        // Check empty slots
        for (ItemStack stack : contents) {
            if (stack == null) {
                remainingAmount -= maxStackSize;
                if (remainingAmount <= 0) return true;
            }
        }
        
        return remainingAmount <= 0;
    }
    
    private String getItemDisplayName(ItemStack item) {
        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            return item.getItemMeta().getDisplayName();
        }
        return item.getType().name().toLowerCase().replace('_', ' ');
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            // Player names
            String partial = args[0].toLowerCase();
            for (OfflinePlayer player : Bukkit.getOfflinePlayers()) {
                if (player.getName() != null && player.getName().toLowerCase().startsWith(partial)) {
                    completions.add(player.getName());
                }
            }
        } else if (args.length == 2) {
            // Item names
            String partial = args[1].toLowerCase();
            completions.add("hand");
            for (Material material : Material.values()) {
                if (material.isItem() && material.name().toLowerCase().startsWith(partial)) {
                    completions.add(material.name().toLowerCase());
                }
            }
        } else if (args.length == 3) {
            // Amount suggestions
            completions.addAll(Arrays.asList("1", "16", "32", "64"));
        }
        
        return completions;
    }
}
