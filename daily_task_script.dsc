 
amuse-barn:
    type: assignment
    actions:
        on assignment:
        - trigger name: click state:true
        on click:
        - ratelimit <player> 5s

        # Check daily task usage first
        - run daily_task_check_reset def:<player>
        - define uses <player.flag[daily_task_uses]||0>
        - define max_uses 3

        # Check if player has uses remaining
        - if <[uses]> >= <[max_uses]>:
            - narrate "<&nl>"
            - narrate "<&d><PERSON>.<&co> <&c>You've already visited me 3 times today! Come back tomorrow."
            - run daily_task_show_reset_time def:<player>
            - stop

        # Increment usage counter
        - flag <player> daily_task_uses:++
        - define new_uses <player.flag[daily_task_uses]>
        - define remaining <[max_uses].sub[<[new_uses]>]>

        # Show remaining uses
        - if <[remaining]> > 0:
            - narrate "<&nl>"
            - narrate "<&d><PERSON>.<&co> <&6>You can find pigs <[remaining]> more time<[remaining].is[!=].to[1].if_true[s].if_false[]> today!"
        - else:
            - narrate "<&nl>"
            - narrate "<&d><PERSON>.<&co> <&6>This was your last visit for today!"

        # Show time until reset
        - run daily_task_show_reset_time def:<player>

        # Original random rewards
        - random:
          - repeat 1:
            - narrate "<&d><PERSON> Jr.<&co> <&f>Lucky you, here's a ride ticket!"
            - narrate "<&nl>"
            - give activity-ticket quantity:1
          - repeat 1:
            - narrate "<&d>Gerald Jr.<&co> <&f>I hope you're hungry, here's a snack ticket!"
            - narrate "<&nl>"
          - repeat 1:
            - narrate "<&d>Gerald Jr.<&co> <&f>Oh! I know what you need, a new <&b>/suffix<&f>!"
            - narrate "<&nl>"
            - execute as_server "lp user <player.uuid> permission set tlb.title.amusement-farmer"
          - repeat 1:
            - narrate "<&d>Gerald Jr.<&co> <&f>Did you think I would have something for you? Funny..."
            - narrate "<&nl>"
          - repeat 1:
            - narrate "<&d>Gerald Jr.<&co> <&f>I hope you're hungry... for nothing!"
            - narrate "<&nl>"
          - repeat 1:
            - narrate "<&d>Gerald Jr.<&co> <&f>Shoo! I am hiding."
            - narrate "<&nl>"
          - repeat 1:
            - narrate "<&d>Gerald Jr.<&co> <&f>Oink..?"
            - narrate "<&nl>"
        - wait 1s
        - playeffect effect:CLOUD at:<npc.location> quantity:50 velocity:10 offset:.0
        - wait 1s
        - run barn-update
        

barn_handler:
    type: world
    events:
      on system time minutes every:15:
      - run barn-update
      
barn-update:
  type: task
  definitions: npc
  script:
      - narrate "Restocking piggies..." targets:<server.match_player[chrismwiggs]>
      - run barn-hide-hide def:<npc[406]>
      - run barn-hide-hide def:<npc[407]>
      - run barn-hide-hide def:<npc[408]>
      - run barn-hide-hide def:<npc[409]>
      - run barn-hide-hide def:<npc[410]>
      - run barn-hide-hide def:<npc[411]>
      - run barn-hide-hide def:<npc[412]>
      - run barn-hide-hide def:<npc[413]>
      - run barn-hide-hide def:<npc[414]>
      - run barn-hide-hide def:<npc[416]>
      - run barn-hide-hide def:<npc[416]>
      - run barn-hide-hide def:<npc[417]>
      - run barn-hide-hide def:<npc[418]>
      - run barn-hide-hide def:<npc[419]>
      - wait 3s
      - foreach <list[406|407|408|409|410|411|412|413|414|415|416|417|418|419].random[3]> as:value:
        - run barn-hide-return def:<npc[<[value]>]>
      
      
barn-hide-hide:
  type: task
  definitions: npc
  script:
  - teleport <npc[<[npc]>]> <server.flag[pigHide]>

barn-hide-return:
  type: task
  definitions: npc
  script:
  - teleport <npc[<[npc]>]> <server.flag[pig<[npc].id>]>

# Daily Task System Scripts
daily_task_world:
    type: world
    debug: false
    events:
        on player joins:
        - run daily_task_check_reset def:<player>

daily_task_check_reset:
    type: task
    debug: false
    definitions: player
    script:
    # Get current date
    - define current_date <util.time_now.format[yyyy-MM-dd]>

    # Get player's last reset date
    - define last_reset <[player].flag[daily_task_last_reset]||null>

    # If it's a new day, reset the counter
    - if <[last_reset]> != <[current_date]>:
        - flag <[player]> daily_task_uses:0
        - flag <[player]> daily_task_last_reset:<[current_date]>

daily_task_show_reset_time:
    type: task
    debug: false
    definitions: player
    script:
    # Calculate time until midnight (simplified approach)
    - define now <util.time_now>
    - define current_hour <[now].format[HH]>
    - define current_minute <[now].format[mm]>

    # Calculate hours and minutes until midnight
    - define hours_left <element[24].sub[<[current_hour]>].sub[1]>
    - define minutes_left <element[60].sub[<[current_minute]>]>

    # Adjust if we're in the last hour
    - if <[current_hour]> == 23:
        - define hours_left 0

    - if <[hours_left]> > 0:
        - narrate "<&6>Daily limit reset in: <&b><[hours_left]> hours and <[minutes_left]> minutes"
        - narrate "<&nl>"
    - else:
        - narrate "<&6>Daily limit reset in: <&b><[minutes_left]> minutes"
        - narrate "<&nl>"

# Optional: Admin command to check/reset player's daily task usage
daily_task_admin:
    type: command
    debug: false
    name: dailytaskadmin
    description: Admin commands for daily task system
    usage: /dailytaskadmin <check|reset> <player>
    permission: dailytask.admin
    tab complete:
    - if <context.args.size> == 1:
        - determine <list[check|reset]>
    - if <context.args.size> == 2:
        - determine <server.online_players.parse[name]>
    script:
    - if <context.args.size> < 2:
        - narrate "<red>Usage: /dailytaskadmin <check|reset> <player>"
        - stop

    - define action <context.args.get[1]>
    - define target_player <server.match_player[<context.args.get[2]>]||null>

    - if <[target_player]> == null:
        - narrate "<red>Player not found!"
        - stop

    - choose <[action]>:
        - case check:
            - define uses <[target_player].flag[daily_task_uses]||0>
            - define last_reset <[target_player].flag[daily_task_last_reset]||never>
            - narrate "<green><[target_player].name> has clicked NPCs <[uses]>/3 times today (last reset: <[last_reset]>)"

        - case reset:
            - flag <[target_player]> daily_task_uses:0
            - define current_date <util.time_now.format[yyyy-MM-dd]>
            - flag <[target_player]> daily_task_last_reset:<[current_date]>
            - narrate "<green>Reset daily NPC clicks for <[target_player].name>"

        - default:
            - narrate "<red>Invalid action! Use 'check' or 'reset'"