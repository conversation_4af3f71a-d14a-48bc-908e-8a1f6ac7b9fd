package com.example.offlineitemdelivery.models;

import org.bukkit.inventory.ItemStack;

import java.sql.Timestamp;
import java.util.UUID;

public class PendingItem {
    
    private int id;
    private UUID playerUuid;
    private String playerName;
    private String serverId;
    private ItemStack itemStack;
    private int amount;
    private Timestamp createdAt;
    private boolean delivered;
    private Timestamp deliveredAt;
    
    // Constructors
    public PendingItem() {}
    
    public PendingItem(UUID playerUuid, String playerName, String serverId, ItemStack itemStack, int amount) {
        this.playerUuid = playerUuid;
        this.playerName = playerName;
        this.serverId = serverId;
        this.itemStack = itemStack;
        this.amount = amount;
        this.delivered = false;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public UUID getPlayerUuid() {
        return playerUuid;
    }
    
    public void setPlayerUuid(UUID playerUuid) {
        this.playerUuid = playerUuid;
    }
    
    public String getPlayerName() {
        return playerName;
    }
    
    public void setPlayerName(String playerName) {
        this.playerName = playerName;
    }
    
    public String getServerId() {
        return serverId;
    }
    
    public void setServerId(String serverId) {
        this.serverId = serverId;
    }
    
    public ItemStack getItemStack() {
        return itemStack;
    }
    
    public void setItemStack(ItemStack itemStack) {
        this.itemStack = itemStack;
    }
    
    public int getAmount() {
        return amount;
    }
    
    public void setAmount(int amount) {
        this.amount = amount;
    }
    
    public Timestamp getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }
    
    public boolean isDelivered() {
        return delivered;
    }
    
    public void setDelivered(boolean delivered) {
        this.delivered = delivered;
    }
    
    public Timestamp getDeliveredAt() {
        return deliveredAt;
    }
    
    public void setDeliveredAt(Timestamp deliveredAt) {
        this.deliveredAt = deliveredAt;
    }
    
    @Override
    public String toString() {
        return "PendingItem{" +
                "id=" + id +
                ", playerUuid=" + playerUuid +
                ", playerName='" + playerName + '\'' +
                ", serverId='" + serverId + '\'' +
                ", amount=" + amount +
                ", delivered=" + delivered +
                '}';
    }
}
