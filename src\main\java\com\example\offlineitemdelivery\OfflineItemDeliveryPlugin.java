package com.example.offlineitemdelivery;

import com.example.offlineitemdelivery.commands.ClaimItemsCommand;
import com.example.offlineitemdelivery.commands.GiveOfflineCommand;
import com.example.offlineitemdelivery.commands.OfflineQueueCommand;
import com.example.offlineitemdelivery.database.DatabaseManager;
import com.example.offlineitemdelivery.listeners.PlayerJoinListener;
import com.example.offlineitemdelivery.services.ItemDeliveryService;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.logging.Level;

public class OfflineItemDeliveryPlugin extends JavaPlugin {
    
    private DatabaseManager databaseManager;
    private ItemDeliveryService itemDeliveryService;
    
    @Override
    public void onEnable() {
        // Save default config
        saveDefaultConfig();
        
        // Initialize database
        try {
            databaseManager = new DatabaseManager(this);
            databaseManager.initialize();
            getLogger().info("Database connection established successfully!");
        } catch (Exception e) {
            getLogger().log(Level.SEVERE, "Failed to initialize database connection!", e);
            getServer().getPluginManager().disablePlugin(this);
            return;
        }
        
        // Initialize services
        itemDeliveryService = new ItemDeliveryService(this, databaseManager);
        
        // Register listeners
        getServer().getPluginManager().registerEvents(new PlayerJoinListener(this, itemDeliveryService), this);
        
        // Register commands
        getCommand("giveoffline").setExecutor(new GiveOfflineCommand(this, itemDeliveryService));
        getCommand("offlinequeue").setExecutor(new OfflineQueueCommand(this, itemDeliveryService));
        getCommand("claimitems").setExecutor(new ClaimItemsCommand(this, itemDeliveryService));
        
        getLogger().info("OfflineItemDelivery plugin has been enabled!");
    }
    
    @Override
    public void onDisable() {
        // Close database connections
        if (databaseManager != null) {
            databaseManager.close();
            getLogger().info("Database connections closed.");
        }
        
        getLogger().info("OfflineItemDelivery plugin has been disabled!");
    }
    
    public DatabaseManager getDatabaseManager() {
        return databaseManager;
    }
    
    public ItemDeliveryService getItemDeliveryService() {
        return itemDeliveryService;
    }
    
    public String getServerId() {
        return getConfig().getString("server.id", "default");
    }
}
