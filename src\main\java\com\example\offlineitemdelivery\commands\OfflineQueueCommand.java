package com.example.offlineitemdelivery.commands;

import com.example.offlineitemdelivery.OfflineItemDeliveryPlugin;
import com.example.offlineitemdelivery.models.PendingItem;
import com.example.offlineitemdelivery.services.ItemDeliveryService;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.OfflinePlayer;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;

import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

public class OfflineQueueCommand implements CommandExecutor, TabCompleter {
    
    private final OfflineItemDeliveryPlugin plugin;
    private final ItemDeliveryService itemDeliveryService;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    public OfflineQueueCommand(OfflineItemDeliveryPlugin plugin, ItemDeliveryService itemDeliveryService) {
        this.plugin = plugin;
        this.itemDeliveryService = itemDeliveryService;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("offlineitemdelivery.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            return true;
        }
        
        if (args.length == 0) {
            sendUsage(sender, label);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "list":
                return handleListCommand(sender, args);
            case "clear":
                return handleClearCommand(sender, args);
            case "remove":
                return handleRemoveCommand(sender, args);
            case "stats":
                return handleStatsCommand(sender, args);
            default:
                sendUsage(sender, label);
                return true;
        }
    }
    
    private boolean handleListCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /offlinequeue list <player>");
            return true;
        }
        
        String playerName = args[1];
        OfflinePlayer targetPlayer = Bukkit.getOfflinePlayer(playerName);
        
        if (targetPlayer == null || (!targetPlayer.hasPlayedBefore() && !targetPlayer.isOnline())) {
            sender.sendMessage(ChatColor.RED + "Player '" + playerName + "' has never played on this server.");
            return true;
        }
        
        try {
            List<PendingItem> pendingItems = itemDeliveryService.getPendingItems(targetPlayer.getUniqueId());
            
            if (pendingItems.isEmpty()) {
                sender.sendMessage(ChatColor.YELLOW + targetPlayer.getName() + " has no pending items.");
                return true;
            }
            
            sender.sendMessage(ChatColor.GREEN + "=== Pending Items for " + targetPlayer.getName() + " ===");
            
            for (int i = 0; i < pendingItems.size(); i++) {
                PendingItem item = pendingItems.get(i);
                String status = item.isDelivered() ? ChatColor.GREEN + "[DELIVERED]" : ChatColor.YELLOW + "[PENDING]";
                String itemName = getItemDisplayName(item);
                String createdAt = dateFormat.format(item.getCreatedAt());
                
                sender.sendMessage(ChatColor.GRAY + (i + 1) + ". " + status + ChatColor.WHITE + " " + 
                    item.getAmount() + "x " + itemName + ChatColor.GRAY + " (ID: " + item.getId() + 
                    ", Server: " + item.getServerId() + ", Created: " + createdAt + ")");
            }
            
            sender.sendMessage(ChatColor.GREEN + "Total: " + pendingItems.size() + " items");
            
        } catch (SQLException e) {
            sender.sendMessage(ChatColor.RED + "Database error occurred while fetching pending items.");
            plugin.getLogger().severe("Database error in list command: " + e.getMessage());
        }
        
        return true;
    }
    
    private boolean handleClearCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /offlinequeue clear <player>");
            return true;
        }
        
        String playerName = args[1];
        OfflinePlayer targetPlayer = Bukkit.getOfflinePlayer(playerName);
        
        if (targetPlayer == null || (!targetPlayer.hasPlayedBefore() && !targetPlayer.isOnline())) {
            sender.sendMessage(ChatColor.RED + "Player '" + playerName + "' has never played on this server.");
            return true;
        }
        
        try {
            int count = itemDeliveryService.getPendingItemCount(targetPlayer.getUniqueId());
            
            if (count == 0) {
                sender.sendMessage(ChatColor.YELLOW + targetPlayer.getName() + " has no pending items to clear.");
                return true;
            }
            
            boolean success = itemDeliveryService.clearPendingItems(targetPlayer.getUniqueId());
            
            if (success) {
                sender.sendMessage(ChatColor.GREEN + "Successfully cleared " + count + " pending items for " + targetPlayer.getName() + ".");
                plugin.getLogger().info(sender.getName() + " cleared " + count + " pending items for " + targetPlayer.getName());
            } else {
                sender.sendMessage(ChatColor.RED + "Failed to clear pending items.");
            }
            
        } catch (SQLException e) {
            sender.sendMessage(ChatColor.RED + "Database error occurred while clearing pending items.");
            plugin.getLogger().severe("Database error in clear command: " + e.getMessage());
        }
        
        return true;
    }
    
    private boolean handleRemoveCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /offlinequeue remove <item_id>");
            return true;
        }
        
        int itemId;
        try {
            itemId = Integer.parseInt(args[1]);
        } catch (NumberFormatException e) {
            sender.sendMessage(ChatColor.RED + "Invalid item ID: " + args[1]);
            return true;
        }
        
        try {
            boolean success = itemDeliveryService.removePendingItem(itemId);
            
            if (success) {
                sender.sendMessage(ChatColor.GREEN + "Successfully removed pending item with ID " + itemId + ".");
                plugin.getLogger().info(sender.getName() + " removed pending item with ID " + itemId);
            } else {
                sender.sendMessage(ChatColor.RED + "No pending item found with ID " + itemId + ".");
            }
            
        } catch (SQLException e) {
            sender.sendMessage(ChatColor.RED + "Database error occurred while removing pending item.");
            plugin.getLogger().severe("Database error in remove command: " + e.getMessage());
        }
        
        return true;
    }
    
    private boolean handleStatsCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /offlinequeue stats <player>");
            return true;
        }
        
        String playerName = args[1];
        OfflinePlayer targetPlayer = Bukkit.getOfflinePlayer(playerName);
        
        if (targetPlayer == null || (!targetPlayer.hasPlayedBefore() && !targetPlayer.isOnline())) {
            sender.sendMessage(ChatColor.RED + "Player '" + playerName + "' has never played on this server.");
            return true;
        }
        
        try {
            int pendingCount = itemDeliveryService.getPendingItemCount(targetPlayer.getUniqueId());
            int maxItems = plugin.getConfig().getInt("settings.max-pending-items", 50);
            
            sender.sendMessage(ChatColor.GREEN + "=== Queue Stats for " + targetPlayer.getName() + " ===");
            sender.sendMessage(ChatColor.WHITE + "Pending Items: " + ChatColor.YELLOW + pendingCount + ChatColor.GRAY + "/" + maxItems);
            sender.sendMessage(ChatColor.WHITE + "Status: " + (targetPlayer.isOnline() ? ChatColor.GREEN + "Online" : ChatColor.RED + "Offline"));
            
        } catch (SQLException e) {
            sender.sendMessage(ChatColor.RED + "Database error occurred while fetching stats.");
            plugin.getLogger().severe("Database error in stats command: " + e.getMessage());
        }
        
        return true;
    }
    
    private void sendUsage(CommandSender sender, String label) {
        sender.sendMessage(ChatColor.GREEN + "=== OfflineItemDelivery Queue Management ===");
        sender.sendMessage(ChatColor.WHITE + "/" + label + " list <player>" + ChatColor.GRAY + " - List pending items for a player");
        sender.sendMessage(ChatColor.WHITE + "/" + label + " clear <player>" + ChatColor.GRAY + " - Clear all pending items for a player");
        sender.sendMessage(ChatColor.WHITE + "/" + label + " remove <item_id>" + ChatColor.GRAY + " - Remove a specific pending item");
        sender.sendMessage(ChatColor.WHITE + "/" + label + " stats <player>" + ChatColor.GRAY + " - Show queue statistics for a player");
    }
    
    private String getItemDisplayName(PendingItem item) {
        if (item.getItemStack().hasItemMeta() && item.getItemStack().getItemMeta().hasDisplayName()) {
            return item.getItemStack().getItemMeta().getDisplayName();
        }
        return item.getItemStack().getType().name().toLowerCase().replace('_', ' ');
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            String partial = args[0].toLowerCase();
            for (String subCommand : Arrays.asList("list", "clear", "remove", "stats")) {
                if (subCommand.startsWith(partial)) {
                    completions.add(subCommand);
                }
            }
        } else if (args.length == 2 && !args[0].equalsIgnoreCase("remove")) {
            String partial = args[1].toLowerCase();
            for (OfflinePlayer player : Bukkit.getOfflinePlayers()) {
                if (player.getName() != null && player.getName().toLowerCase().startsWith(partial)) {
                    completions.add(player.getName());
                }
            }
        }
        
        return completions;
    }
}
