# OfflineItemDelivery Plugin

A Spigot plugin designed to deliver items to offline players with MySQL persistence and multi-server support.

## Features

- **Offline Item Delivery**: Queue items for players who are offline
- **MySQL Persistence**: Items are stored in a MySQL database for cross-server compatibility
- **Per-Server Tracking**: Items are delivered only once per server to prevent duplicates
- **Inventory Management**: Automatically checks for inventory space before delivery
- **Queue Management**: Comprehensive commands to manage the item delivery queue
- **Configurable Limits**: Set maximum pending items per player
- **Detailed Logging**: Track all deliveries, failures, and queue operations

## Requirements

- Spigot/Paper 1.20.1 or higher
- Java 8 or higher
- MySQL 5.7 or higher (or MariaDB equivalent)

## Installation

1. Download the plugin JAR file
2. Place it in your server's `plugins` folder
3. Configure your MySQL database settings in `config.yml`
4. Restart your server
5. The plugin will automatically create the necessary database tables

## Configuration

Edit `plugins/OfflineItemDelivery/config.yml`:

```yaml
# Database Configuration
database:
  host: "localhost"
  port: 3306
  database: "minecraft"
  username: "minecraft"
  password: "password"
  
  # Connection pool settings
  pool:
    maximum-pool-size: 10
    minimum-idle: 2
    connection-timeout: 30000
    idle-timeout: 600000
    max-lifetime: 1800000

# Server Configuration
server:
  # Unique identifier for this server (IMPORTANT: Change this for each server)
  id: "server1"

# Plugin Settings
settings:
  # Maximum number of pending items per player
  max-pending-items: 50
  
  # Whether to notify players when they join about pending items
  notify-on-join: true
  
  # Messages (supports color codes with &)
  inventory-full-message: "&cYour inventory is full! You have pending items waiting. Make some space and rejoin to receive them."
  delivery-success-message: "&aYou have received &e{amount}x {item}&a!"
  pending-items-message: "&eYou have &b{count}&e pending item(s) waiting for delivery!"

# Logging Settings
logging:
  log-deliveries: true
  log-failures: true
  log-database: false
```

## Commands

### `/giveoffline <player> <item> [amount] [data]`
- **Permission**: `offlineitemdelivery.give`
- **Description**: Give an item to an offline player
- **Aliases**: `/giveoff`, `/offlinegive`
- **Examples**:
  - `/giveoffline Steve diamond_sword 1` - Give Steve a diamond sword
  - `/giveoffline Alex apple 64` - Give Alex 64 apples
  - `/giveoffline Bob hand` - Give Bob the item you're holding

### `/offlinequeue <list|clear|remove|stats> [player|item_id]`
- **Permission**: `offlineitemdelivery.admin`
- **Description**: Manage the offline item delivery queue
- **Aliases**: `/oqueue`, `/itemqueue`
- **Subcommands**:
  - `list <player>` - List all pending items for a player
  - `clear <player>` - Clear all pending items for a player
  - `remove <item_id>` - Remove a specific pending item by ID
  - `stats <player>` - Show queue statistics for a player

## Permissions

- `offlineitemdelivery.give` - Allows giving items to offline players (default: op)
- `offlineitemdelivery.admin` - Allows managing the offline item queue (default: op)
- `offlineitemdelivery.*` - Gives access to all plugin permissions (default: op)

## Database Schema

The plugin creates a table called `offline_item_queue` with the following structure:

```sql
CREATE TABLE offline_item_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    player_uuid VARCHAR(36) NOT NULL,
    player_name VARCHAR(16) NOT NULL,
    server_id VARCHAR(50) NOT NULL,
    item_data TEXT NOT NULL,
    amount INT NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    delivered BOOLEAN DEFAULT FALSE,
    delivered_at TIMESTAMP NULL,
    INDEX idx_player_uuid (player_uuid),
    INDEX idx_server_id (server_id),
    INDEX idx_delivered (delivered)
);
```

## Multi-Server Setup

To use this plugin across multiple servers:

1. Set up a shared MySQL database accessible by all servers
2. Configure each server with the same database connection details
3. **Important**: Set a unique `server.id` in each server's config.yml
4. Items will be delivered only once per server, preventing duplicates

Example server IDs:
- Server 1: `survival`
- Server 2: `creative`
- Server 3: `skyblock`

## How It Works

1. **Queuing Items**: When you use `/giveoffline`, the item is serialized and stored in the MySQL database
2. **Player Join**: When a player joins, the plugin checks for pending items for that specific server
3. **Inventory Check**: Before delivery, the plugin verifies the player has enough inventory space
4. **Delivery**: Items are given to the player and marked as delivered in the database
5. **Full Inventory**: If the player's inventory is full, they're notified and items remain queued

## Building from Source

1. Clone the repository
2. Ensure you have Maven installed
3. Run `mvn clean package`
4. The compiled JAR will be in the `target` folder

## Support

If you encounter any issues:

1. Check the server console for error messages
2. Verify your MySQL connection settings
3. Ensure the database user has proper permissions
4. Check that the `server.id` is unique for each server

## License

This plugin is provided as-is for educational and server use purposes.
