# OfflineItemDelivery Configuration

# Database Configuration
database:
  host: "localhost"
  port: 3306
  database: "minecraft"
  username: "minecraft"
  password: "password"
  
  # Connection pool settings
  pool:
    maximum-pool-size: 10
    minimum-idle: 2
    connection-timeout: 30000
    idle-timeout: 600000
    max-lifetime: 1800000

# Server Configuration
server:
  # Unique identifier for this server (used to track per-server deliveries)
  # Change this for each server in your network
  id: "server1"

  # List of known servers in your network (used for tab completion)
  # Add all your server names here for better user experience
  known-servers:
    - "survival"
    - "creative"
    - "skyblock"
    - "factions"
    - "prison"
    - "hub"

# Plugin Settings
settings:
  # Message prefix for all plugin messages
  prefix: "&6[&bRedeem&6] "

  # Maximum number of pending items per player
  max-pending-items: 50

  # Whether to notify players when they join about pending items
  notify-on-join: true
  
  # Message sent when player inventory is full
  inventory-full-message: "&cYour inventory is full! You have pending items waiting. Make some space and rejoin to receive them."
  
  # Message sent when items are successfully delivered
  delivery-success-message: "&aYou have received &e{amount}x {item}&a!"
  
  # Message sent when player has pending items on join
  pending-items-message: "&eYou have &b{count}&e pending item(s) waiting for delivery!"

  # Message sent when player successfully claims items
  claim-success-message: "&aSuccessfully claimed &e{count}&a item(s)!"

  # Message sent when player has no items to claim
  no-items-message: "&eYou have no pending items to claim!"

  # Minimum empty slots recommended before claiming (warning threshold)
  min-empty-slots-warning: 5

  # Message when items can't be delivered due to full inventory
  inventory-full-remaining-message: "&eYou still have &b{count}&e pending item(s) that couldn't be delivered because your inventory is full."

  # Message when items can't be delivered due to server restrictions
  server-restricted-message: "&eYou still have &b{count}&e pending item(s) that couldn't be delivered on this server."
  server-restricted-help: "&bThese items may be restricted to a different server. Try joining other servers in the network to claim them."

# Logging Settings
logging:
  # Log all item deliveries
  log-deliveries: true
  
  # Log failed delivery attempts
  log-failures: true
  
  # Log database operations (for debugging)
  log-database: false
