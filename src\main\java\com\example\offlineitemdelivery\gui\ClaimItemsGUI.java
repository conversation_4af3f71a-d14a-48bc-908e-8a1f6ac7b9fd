package com.example.offlineitemdelivery.gui;

import com.example.offlineitemdelivery.OfflineItemDeliveryPlugin;
import com.example.offlineitemdelivery.integrations.ItemsAdderIntegration;
import com.example.offlineitemdelivery.models.PendingItem;
import com.example.offlineitemdelivery.services.ItemDeliveryService;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ClaimItemsGUI implements Listener {
    
    private final OfflineItemDeliveryPlugin plugin;
    private final ItemDeliveryService itemDeliveryService;
    private final Map<Player, List<PendingItem>> playerItems = new HashMap<>();
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    
    public ClaimItemsGUI(OfflineItemDeliveryPlugin plugin, ItemDeliveryService itemDeliveryService) {
        this.plugin = plugin;
        this.itemDeliveryService = itemDeliveryService;
    }
    
    /**
     * Opens the claim items GUI for a player
     */
    public void openClaimGUI(Player player) {
        try {
            List<PendingItem> pendingItems = itemDeliveryService.getPendingItems(player.getUniqueId());
            
            if (pendingItems.isEmpty()) {
                String message = plugin.getConfig().getString("settings.no-items-message", 
                    "&eYou have no pending items to claim!");
                plugin.sendMessage(player, message);
                return;
            }
            
            // Store items for this player
            playerItems.put(player, pendingItems);
            
            // Create inventory
            int size = Math.min(54, ((pendingItems.size() + 8) / 9) * 9); // Round up to nearest 9, max 54
            Inventory inventory = Bukkit.createInventory(null, size, 
                ChatColor.translateAlternateColorCodes('&', "&b&lPending Items &7(" + pendingItems.size() + ")"));
            
            // Add items to inventory
            for (int i = 0; i < pendingItems.size() && i < 54; i++) {
                PendingItem pendingItem = pendingItems.get(i);
                ItemStack displayItem = createDisplayItem(pendingItem, player);
                inventory.setItem(i, displayItem);
            }
            
            player.openInventory(inventory);
            
        } catch (SQLException e) {
            plugin.sendMessage(player, "&cAn error occurred while loading your pending items.");
            plugin.getLogger().severe("Database error opening claim GUI for " + player.getName() + ": " + e.getMessage());
        }
    }
    
    /**
     * Creates a display item for the GUI with proper lore
     */
    private ItemStack createDisplayItem(PendingItem pendingItem, Player player) {
        ItemStack originalItem = pendingItem.getItemStack();
        if (originalItem == null) {
            // Fallback for corrupted items
            ItemStack fallback = new ItemStack(Material.BARRIER);
            ItemMeta meta = fallback.getItemMeta();
            meta.setDisplayName(ChatColor.RED + "Corrupted Item");
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "This item could not be loaded");
            lore.add(ChatColor.GRAY + "ID: " + pendingItem.getId());
            meta.setLore(lore);
            fallback.setItemMeta(meta);
            return fallback;
        }
        
        // Clone the original item
        ItemStack displayItem = originalItem.clone();
        displayItem.setAmount(pendingItem.getAmount());
        
        // Get or create item meta
        ItemMeta meta = displayItem.getItemMeta();
        if (meta == null) {
            meta = Bukkit.getItemFactory().getItemMeta(displayItem.getType());
        }
        
        // Set display name if not already set
        if (!meta.hasDisplayName()) {
            String itemName = getItemDisplayName(pendingItem);
            meta.setDisplayName(ChatColor.WHITE + itemName);
        }
        
        // Create lore
        List<String> lore = new ArrayList<>();
        
        // Add existing lore if any
        if (meta.hasLore()) {
            lore.addAll(meta.getLore());
            lore.add(""); // Separator
        }
        
        // Add server information
        String currentServerId = plugin.getServerId();
        if (pendingItem.getServerId().equals(currentServerId)) {
            lore.add(ChatColor.GREEN + "✓ Available on this server");
            lore.add(ChatColor.GRAY + "Click to claim this item!");
        } else {
            lore.add(ChatColor.RED + "✗ Only available on: " + ChatColor.YELLOW + pendingItem.getServerId());
            lore.add(ChatColor.GRAY + "Join that server to claim this item");
        }
        
        // Add item ID for debugging
        lore.add("");
        lore.add(ChatColor.DARK_GRAY + "ID: " + pendingItem.getId());
        
        meta.setLore(lore);
        displayItem.setItemMeta(meta);
        
        return displayItem;
    }
    
    /**
     * Gets the display name for an item (handles ItemsAdder items)
     */
    private String getItemDisplayName(PendingItem item) {
        if (item == null || item.getItemStack() == null) {
            return "Unknown Item";
        }
        
        try {
            // First check if it's an ItemsAdder item
            if (ItemsAdderIntegration.isEnabled() && ItemsAdderIntegration.isCustomItem(item.getItemStack())) {
                String customItemId = ItemsAdderIntegration.getCustomItemId(item.getItemStack());
                if (customItemId != null) {
                    return ItemsAdderIntegration.getItemDisplayName(customItemId);
                }
            }
            
            // Fall back to vanilla item handling
            if (item.getItemStack().hasItemMeta() && item.getItemStack().getItemMeta().hasDisplayName()) {
                return ChatColor.stripColor(item.getItemStack().getItemMeta().getDisplayName());
            }
            return item.getItemStack().getType().name().toLowerCase().replace('_', ' ');
        } catch (Exception e) {
            return "Corrupted Item";
        }
    }
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        
        // Check if this is our GUI
        if (!event.getView().getTitle().contains("Pending Items")) {
            return;
        }
        
        event.setCancelled(true); // Prevent item movement
        
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) {
            return;
        }
        
        // Get the player's pending items
        List<PendingItem> pendingItems = playerItems.get(player);
        if (pendingItems == null) {
            return;
        }
        
        int slot = event.getSlot();
        if (slot >= pendingItems.size()) {
            return;
        }
        
        PendingItem pendingItem = pendingItems.get(slot);
        
        // Check if item is claimable on this server
        if (!pendingItem.getServerId().equals(plugin.getServerId())) {
            plugin.sendMessage(player, "&cThis item is only available on the &e" + pendingItem.getServerId() + "&c server!");
            return;
        }
        
        // Try to deliver the item
        if (attemptSingleItemDelivery(player, pendingItem)) {
            // Remove from GUI and update
            pendingItems.remove(slot);
            player.closeInventory();
            
            // Reopen GUI if there are more items
            if (!pendingItems.isEmpty()) {
                Bukkit.getScheduler().runTaskLater(plugin, () -> openClaimGUI(player), 1L);
            } else {
                plugin.sendMessage(player, "&aAll items claimed successfully!");
                playerItems.remove(player);
            }
        }
    }
    
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getPlayer() instanceof Player) {
            Player player = (Player) event.getPlayer();
            if (event.getView().getTitle().contains("Pending Items")) {
                // Clean up after a short delay to allow for reopening
                Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    if (!player.getOpenInventory().getTitle().contains("Pending Items")) {
                        playerItems.remove(player);
                    }
                }, 5L);
            }
        }
    }
    
    /**
     * Attempts to deliver a single item to a player
     */
    private boolean attemptSingleItemDelivery(Player player, PendingItem pendingItem) {
        try {
            ItemStack itemToGive = pendingItem.getItemStack().clone();
            itemToGive.setAmount(pendingItem.getAmount());
            
            // Check if player has space
            if (!hasInventorySpace(player, itemToGive)) {
                plugin.sendMessage(player, "&cYour inventory is full! Make some space and try again.");
                return false;
            }
            
            // Give the item
            player.getInventory().addItem(itemToGive);
            
            // Mark as delivered in database
            itemDeliveryService.markItemAsDelivered(pendingItem.getId());
            
            // Send success message
            String message = plugin.getConfig().getString("settings.delivery-success-message", 
                "&aYou have received &e{amount}x {item}&a!");
            message = message
                .replace("{amount}", String.valueOf(pendingItem.getAmount()))
                .replace("{item}", getItemDisplayName(pendingItem));
            plugin.sendMessage(player, message);
            
            return true;
            
        } catch (Exception e) {
            plugin.sendMessage(player, "&cAn error occurred while claiming this item.");
            plugin.getLogger().severe("Error delivering item to " + player.getName() + ": " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Checks if player has enough inventory space for an item
     */
    private boolean hasInventorySpace(Player player, ItemStack item) {
        ItemStack[] contents = player.getInventory().getStorageContents().clone();
        int remainingAmount = item.getAmount();
        int maxStackSize = item.getMaxStackSize();
        
        // Check existing stacks
        for (ItemStack stack : contents) {
            if (stack != null && stack.isSimilar(item)) {
                int spaceInStack = maxStackSize - stack.getAmount();
                if (spaceInStack > 0) {
                    remainingAmount -= spaceInStack;
                    if (remainingAmount <= 0) return true;
                }
            }
        }
        
        // Check empty slots
        for (ItemStack stack : contents) {
            if (stack == null) {
                remainingAmount -= maxStackSize;
                if (remainingAmount <= 0) return true;
            }
        }
        
        return remainingAmount <= 0;
    }
}
