package com.example.offlineitemdelivery.listeners;

import com.example.offlineitemdelivery.OfflineItemDeliveryPlugin;
import com.example.offlineitemdelivery.services.ItemDeliveryService;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;

public class PlayerJoinListener implements Listener {
    
    private final OfflineItemDeliveryPlugin plugin;
    private final ItemDeliveryService itemDeliveryService;
    
    public PlayerJoinListener(OfflineItemDeliveryPlugin plugin, ItemDeliveryService itemDeliveryService) {
        this.plugin = plugin;
        this.itemDeliveryService = itemDeliveryService;
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        // Delay the delivery slightly to ensure player is fully loaded
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            itemDeliveryService.deliverPendingItems(event.getPlayer());
        }, 20L); // 1 second delay
    }
}
